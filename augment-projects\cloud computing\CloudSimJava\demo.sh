#!/bin/bash

echo "Cloud Storage Simulation - Demo <PERSON>t"
echo "====================================="
echo
echo "This script demonstrates the cloud storage simulation system."
echo
echo "Instructions:"
echo "1. First, build the project: mvn clean package"
echo "2. Then run this script to see the system in action"
echo

if [ ! -f "target/cloud-storage-simulation-1.0.0.jar" ]; then
    echo "ERROR: JAR file not found. Please run 'mvn clean package' first."
    exit 1
fi

echo "Starting Network Controller..."
java -jar target/cloud-storage-simulation-1.0.0.jar --network --port 8080 &
NETWORK_PID=$!

echo "Waiting for network controller to start..."
sleep 3

echo "Starting Storage Node 1..."
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node1 --cpu 4 --memory 8 --storage 100 --bandwidth 1000 &
NODE1_PID=$!

echo "Waiting for node 1 to register..."
sleep 2

echo "Starting Storage Node 2..."
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node2 --cpu 8 --memory 16 --storage 200 --bandwidth 2000 &
NODE2_PID=$!

echo "Waiting for node 2 to register..."
sleep 2

echo "Starting Storage Node 3..."
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node3 --cpu 2 --memory 4 --storage 50 --bandwidth 500 &
NODE3_PID=$!

echo
echo "Demo started! Process IDs:"
echo "- Network Controller: $NETWORK_PID"
echo "- Storage Node 1: $NODE1_PID"
echo "- Storage Node 2: $NODE2_PID"
echo "- Storage Node 3: $NODE3_PID"
echo
echo "Watch the logs to see:"
echo "- Node registration"
echo "- Heartbeat exchanges"
echo "- Resource reporting"
echo
echo "Press Ctrl+C to stop all processes..."

# Function to cleanup processes on exit
cleanup() {
    echo
    echo "Stopping all processes..."
    kill $NETWORK_PID $NODE1_PID $NODE2_PID $NODE3_PID 2>/dev/null
    wait
    echo "Demo stopped."
}

# Set trap to cleanup on exit
trap cleanup EXIT INT TERM

# Wait for user to stop
wait