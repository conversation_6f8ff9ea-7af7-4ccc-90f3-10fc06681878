# Distributed Cloud Storage Simulation - Project Report

**Student Name:** [Your Name]
**Course:** Cloud Computing and Distributed Systems
**Date:** August 2025

## Project Overview

For this assignment, I implemented a distributed cloud storage simulation system in Java. The goal was to understand how distributed storage systems work by building a simplified version that demonstrates key concepts like node coordination, file chunking, and fault tolerance.

## What I Learned

This project helped me understand several important concepts:

1. **Distributed Systems Architecture** - How multiple computers work together
2. **Network Programming** - Socket communication between nodes
3. **Load Balancing** - Distributing work across multiple servers
4. **Fault Tolerance** - Handling server failures gracefully
5. **Resource Management** - Tracking and managing system resources

## Implementation Details

### Core Components

I designed the system with these main components:

- **StorageVirtualNode**: Represents individual storage servers
- **StorageVirtualNetwork**: Central controller that manages all nodes
- **FileTransferOrchestrator**: Handles file distribution logic
- **Various data models**: For representing files, chunks, and node information

### Key Features Implemented

1. **Node Registration**: Storage nodes automatically register with the controller
2. **Heartbeat System**: Nodes send periodic "alive" messages
3. **File Chunking**: Large files are split into smaller pieces for distribution
4. **Load Balancing**: Files are distributed based on available node capacity
5. **Resource Monitoring**: System tracks storage usage and network bandwidth

## Challenges Faced

During development, I encountered several challenges:

1. **Threading Issues**: Managing multiple concurrent connections was tricky
2. **Network Communication**: Getting the socket communication working properly
3. **Resource Tracking**: Accurately monitoring storage and bandwidth usage
4. **Error Handling**: Making the system robust against various failure scenarios

## How It Works

The system works as follows:

1. Start the network controller first
2. Launch multiple storage nodes that register with the controller
3. Nodes send heartbeat messages to prove they're alive
4. When files need to be stored, the system:
   - Breaks files into chunks
   - Selects appropriate nodes based on available capacity
   - Distributes chunks across multiple nodes for redundancy
   - Monitors transfer progress

## Testing

I tested the system by:

1. Starting one controller and multiple storage nodes
2. Observing the registration and heartbeat processes
3. Simulating node failures by stopping nodes
4. Verifying that the system correctly detects failed nodes
5. Testing with different node configurations (CPU, memory, storage, bandwidth)

## Results

The simulation successfully demonstrates:

- Automatic node discovery and registration
- Fault detection through heartbeat monitoring
- Basic load balancing based on node capacity
- Resource utilization tracking
- Graceful handling of node failures

## Future Improvements

If I had more time, I would add:

1. **Actual File Storage**: Currently it only simulates transfers
2. **Web Interface**: A dashboard to monitor the system
3. **Data Replication**: Automatic backup of files across nodes
4. **Performance Metrics**: Detailed statistics and monitoring
5. **Advanced Load Balancing**: More sophisticated node selection algorithms

## Conclusion

This project gave me hands-on experience with distributed systems concepts. I now better understand how cloud storage services like Amazon S3 or Google Drive work internally. The simulation demonstrates the complexity involved in building reliable distributed systems and the importance of proper design for scalability and fault tolerance.

The code is well-structured and documented, making it easy to extend with additional features. Overall, this was a valuable learning experience that reinforced the theoretical concepts covered in class.

## How to Run

1. Build the project: `mvn clean package`
2. Start network controller: `java -jar target/cloud-storage-simulation-1.0.0.jar --network`
3. Start storage nodes: `java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node1`
4. Observe the logs to see the system in action

## References

- Course lecture materials on distributed systems
- Java documentation for networking and threading
- Research papers on distributed storage systems
- Online tutorials for Maven and project structure