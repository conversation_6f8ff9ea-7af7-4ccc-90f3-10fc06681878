@echo off
echo Building Cloud Storage Simulation Project
echo =========================================

echo Cleaning previous build...
mvn clean

echo Compiling source code...
mvn compile

echo Creating JAR file...
mvn package

echo.
if exist "target\cloud-storage-simulation-1.0.0.jar" (
    echo Build successful! JAR file created.
    echo You can now run the demo with: demo.bat
) else (
    echo Build failed! Check the error messages above.
)

pause