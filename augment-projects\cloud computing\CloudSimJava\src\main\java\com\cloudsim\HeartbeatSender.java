package com.cloudsim;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.Socket;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * Sends periodic heartbeat messages to the network controller.
 */
public class HeartbeatSender extends Thread {
    private static final Logger logger = LoggerFactory.getLogger(HeartbeatSender.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int HEARTBEAT_INTERVAL_SECONDS = 2;

    private final String nodeId;
    private final String networkHost;
    private final int networkPort;
    private volatile boolean running = false;

    /**
     * Creates a new heartbeat sender.
     *
     * @param nodeId      Node identifier
     * @param networkHost Network controller host
     * @param networkPort Network controller port
     */
    public HeartbeatSender(String nodeId, String networkHost, int networkPort) {
        super("HeartbeatSender-" + nodeId);
        this.nodeId = nodeId;
        this.networkHost = networkHost;
        this.networkPort = networkPort;
        setDaemon(true);
    }

    @Override
    public void run() {
        running = true;
        logger.info("Heartbeat sender for node {} started", nodeId);

        while (running && !Thread.currentThread().isInterrupted()) {
            try {
                sendHeartbeat();
                TimeUnit.SECONDS.sleep(HEARTBEAT_INTERVAL_SECONDS);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                if (running) {
                    logger.warn("Failed to send heartbeat for node {}: {}", nodeId, e.getMessage());
                }
            }
        }

        logger.info("Heartbeat sender for node {} stopped", nodeId);
    }

    /**
     * Sends a heartbeat message to the network controller.
     */
    private void sendHeartbeat() {
        try (Socket socket = new Socket(networkHost, networkPort)) {
            socket.setSoTimeout(3000); // 3 second timeout

            Map<String, Object> message = new HashMap<>();
            message.put("action", "HEARTBEAT");
            message.put("node_id", nodeId);

            // Send message
            try (ObjectOutputStream out = new ObjectOutputStream(socket.getOutputStream())) {
                out.writeObject(message);
                out.flush();

                // Read response
                try (ObjectInputStream in = new ObjectInputStream(socket.getInputStream())) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> response = (Map<String, Object>) in.readObject();

                    if (!"ACK".equals(response.get("status"))) {
                        logger.warn("Heartbeat for node {} not acknowledged: {}", nodeId, response);
                    }
                }
            }

        } catch (IOException | ClassNotFoundException e) {
            if (running) {
                logger.debug("Heartbeat failed for node {}: {}", nodeId, e.getMessage());
            }
        }
    }

    /**
     * Stops the heartbeat sender.
     */
    public void stopSender() {
        running = false;
        interrupt();
    }
}