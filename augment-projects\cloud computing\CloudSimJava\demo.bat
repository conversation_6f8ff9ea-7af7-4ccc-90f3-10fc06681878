@echo off
echo Cloud Storage Simulation - Demo Script
echo =====================================
echo.
echo This script demonstrates the cloud storage simulation system.
echo.
echo Instructions:
echo 1. First, build the project: mvn clean package
echo 2. Then run this script to see the system in action
echo.

if not exist "target\cloud-storage-simulation-1.0.0.jar" (
    echo ERROR: JAR file not found. Please run 'mvn clean package' first.
    pause
    exit /b 1
)

echo Starting Network Controller...
start "Network Controller" java -jar target\cloud-storage-simulation-1.0.0.jar --network --port 8080

echo Waiting for network controller to start...
timeout /t 3 /nobreak > nul

echo Starting Storage Node 1...
start "Storage Node 1" java -jar target\cloud-storage-simulation-1.0.0.jar --node --node-id node1 --cpu 4 --memory 8 --storage 100 --bandwidth 1000

echo Waiting for node 1 to register...
timeout /t 2 /nobreak > nul

echo Starting Storage Node 2...
start "Storage Node 2" java -jar target\cloud-storage-simulation-1.0.0.jar --node --node-id node2 --cpu 8 --memory 16 --storage 200 --bandwidth 2000

echo Waiting for node 2 to register...
timeout /t 2 /nobreak > nul

echo Starting Storage Node 3...
start "Storage Node 3" java -jar target\cloud-storage-simulation-1.0.0.jar --node --node-id node3 --cpu 2 --memory 4 --storage 50 --bandwidth 500

echo.
echo Demo started! You should now see:
echo - 1 Network Controller window
echo - 3 Storage Node windows
echo.
echo Watch the logs to see:
echo - Node registration
echo - Heartbeat exchanges
echo - Resource reporting
echo.
echo Press Ctrl+C in each window to stop the simulation.
echo.
pause