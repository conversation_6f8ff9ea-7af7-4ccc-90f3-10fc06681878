package com.cloudsim;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Network controller that manages the distributed storage network.
 * Handles node registration, heartbeat monitoring, and network topology.
 */
public class StorageVirtualNetwork {
    private static final Logger logger = LoggerFactory.getLogger(StorageVirtualNetwork.class);
    private static final int HEARTBEAT_TIMEOUT_SECONDS = 10;
    private static final int HEARTBEAT_CHECK_INTERVAL_SECONDS = 5;

    private final int port;
    private final Map<String, NodeInfo> registeredNodes = new ConcurrentHashMap<>();
    private final Map<String, Instant> lastHeartbeat = new ConcurrentHashMap<>();
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong totalDataTransferred = new AtomicLong(0);

    private ServerSocket serverSocket;
    private final ExecutorService executorService;
    private final ScheduledExecutorService scheduledExecutor;
    private volatile boolean running = false;

    /**
     * Creates a new storage virtual network controller.
     *
     * @param port Port to bind the server to
     */
    public StorageVirtualNetwork(int port) {
        this.port = port;
        this.executorService = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "NetworkController-Worker");
            t.setDaemon(true);
            return t;
        });
        this.scheduledExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "NetworkController-Scheduler");
            t.setDaemon(true);
            return t;
        });

        logger.info("Created network controller on port {}", port);
    }

    /**
     * Starts the network controller server.
     */
    public void start() {
        if (running) {
            logger.warn("Network controller is already running");
            return;
        }

        running = true;
        logger.info("Starting network controller on port {}", port);

        try {
            serverSocket = new ServerSocket(port);
            logger.info("Network controller listening on port {}", serverSocket.getLocalPort());

            // Start heartbeat monitoring
            startHeartbeatMonitoring();

            // Accept client connections
            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    Socket clientSocket = serverSocket.accept();
                    totalConnections.incrementAndGet();
                    executorService.submit(() -> handleClientConnection(clientSocket));
                } catch (IOException e) {
                    if (running) {
                        logger.error("Error accepting client connection: {}", e.getMessage());
                    }
                }
            }

        } catch (IOException e) {
            logger.error("Failed to start network controller: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to start network controller", e);
        } finally {
            shutdown();
        }
    }

    /**
     * Starts heartbeat monitoring in a separate thread.
     */
    private void startHeartbeatMonitoring() {
        scheduledExecutor.scheduleWithFixedDelay(
                this::checkHeartbeats,
                HEARTBEAT_CHECK_INTERVAL_SECONDS,
                HEARTBEAT_CHECK_INTERVAL_SECONDS,
                TimeUnit.SECONDS
        );
        logger.info("Started heartbeat monitoring");
    }

    /**
     * Handles a client connection from a storage node.
     *
     * @param clientSocket Socket connection to the client
     */
    private void handleClientConnection(Socket clientSocket) {
        try (ObjectInputStream in = new ObjectInputStream(clientSocket.getInputStream());
             ObjectOutputStream out = new ObjectOutputStream(clientSocket.getOutputStream())) {

            @SuppressWarnings("unchecked")
            Map<String, Object> message = (Map<String, Object>) in.readObject();
            String action = (String) message.get("action");

            Map<String, Object> response = new HashMap<>();

            switch (action) {
                case "REGISTER":
                    handleNodeRegistration(message, response);
                    break;
                case "HEARTBEAT":
                    handleHeartbeat(message, response);
                    break;
                case "ACTIVE_NOTIFICATION":
                    handleActiveNotification(message, response);
                    break;
                default:
                    response.put("status", "ERROR");
                    response.put("message", "Unknown action: " + action);
                    logger.warn("Unknown action received: {}", action);
            }

            out.writeObject(response);
            out.flush();

        } catch (Exception e) {
            logger.error("Error handling client connection: {}", e.getMessage(), e);
        } finally {
            try {
                clientSocket.close();
            } catch (IOException e) {
                logger.debug("Error closing client socket: {}", e.getMessage());
            }
        }
    }

    /**
     * Handles node registration requests.
     *
     * @param message  Registration message from the node
     * @param response Response to send back
     */
    private void handleNodeRegistration(Map<String, Object> message, Map<String, Object> response) {
        try {
            String nodeId = (String) message.get("node_id");
            String host = (String) message.get("host");
            Integer port = (Integer) message.get("port");

            @SuppressWarnings("unchecked")
            Map<String, Object> capacityMap = (Map<String, Object>) message.get("capacity");

            NodeCapacity capacity = new NodeCapacity(
                    (Integer) capacityMap.get("cpu"),
                    (Integer) capacityMap.get("memory"),
                    ((Number) capacityMap.get("storage")).longValue(),
                    ((Number) capacityMap.get("bandwidth")).longValue()
            );

            NodeInfo nodeInfo = new NodeInfo(nodeId, host, port, capacity);
            registeredNodes.put(nodeId, nodeInfo);
            lastHeartbeat.put(nodeId, Instant.now());

            response.put("status", "OK");
            response.put("message", "Node registered successfully");

            logger.info("Registered node {} with capacity: {}", nodeId, capacity);

        } catch (Exception e) {
            response.put("status", "ERROR");
            response.put("message", "Registration failed: " + e.getMessage());
            logger.error("Node registration failed: {}", e.getMessage(), e);
        }
    }

    /**
     * Handles heartbeat messages from nodes.
     *
     * @param message  Heartbeat message from the node
     * @param response Response to send back
     */
    private void handleHeartbeat(Map<String, Object> message, Map<String, Object> response) {
        String nodeId = (String) message.get("node_id");

        if (registeredNodes.containsKey(nodeId)) {
            lastHeartbeat.put(nodeId, Instant.now());
            registeredNodes.get(nodeId).updateLastSeen();
            response.put("status", "ACK");
            logger.debug("Heartbeat received from node {}", nodeId);
        } else {
            response.put("status", "ERROR");
            response.put("message", "Node not registered");
            logger.warn("Heartbeat from unregistered node: {}", nodeId);
        }
    }

    /**
     * Handles active notification messages from nodes.
     *
     * @param message  Active notification message from the node
     * @param response Response to send back
     */
    private void handleActiveNotification(Map<String, Object> message, Map<String, Object> response) {
        String nodeId = (String) message.get("node_id");

        if (registeredNodes.containsKey(nodeId)) {
            NodeInfo nodeInfo = registeredNodes.get(nodeId);
            nodeInfo.setActive(true);
            nodeInfo.updateLastSeen();
            lastHeartbeat.put(nodeId, Instant.now());

            response.put("status", "ACK");
            logger.info("Node {} is now active", nodeId);
        } else {
            response.put("status", "ERROR");
            response.put("message", "Node not registered");
            logger.warn("Active notification from unregistered node: {}", nodeId);
        }
    }

    /**
     * Checks heartbeats and marks inactive nodes.
     */
    private void checkHeartbeats() {
        Instant now = Instant.now();
        Instant timeout = now.minusSeconds(HEARTBEAT_TIMEOUT_SECONDS);

        for (Map.Entry<String, Instant> entry : lastHeartbeat.entrySet()) {
            String nodeId = entry.getKey();
            Instant lastSeen = entry.getValue();

            if (lastSeen.isBefore(timeout)) {
                NodeInfo nodeInfo = registeredNodes.get(nodeId);
                if (nodeInfo != null && nodeInfo.isActive()) {
                    nodeInfo.setActive(false);
                    logger.warn("Node {} marked as inactive (last seen: {})", nodeId, lastSeen);
                }
            }
        }
    }

    /**
     * Gets network statistics.
     *
     * @return map containing network statistics
     */
    public Map<String, Object> getNetworkStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("total_nodes", registeredNodes.size());
        stats.put("active_nodes", registeredNodes.values().stream().mapToLong(n -> n.isActive() ? 1 : 0).sum());
        stats.put("total_connections", totalConnections.get());
        stats.put("total_data_transferred", totalDataTransferred.get());

        // Calculate total capacity
        long totalStorage = registeredNodes.values().stream()
                .mapToLong(n -> n.getCapacity().getStorage())
                .sum();
        long totalBandwidth = registeredNodes.values().stream()
                .mapToLong(n -> n.getCapacity().getBandwidth())
                .sum();

        stats.put("total_storage_capacity", totalStorage);
        stats.put("total_bandwidth_capacity", totalBandwidth);

        return stats;
    }

    /**
     * Gets information about all registered nodes.
     *
     * @return map of node information
     */
    public Map<String, NodeInfo> getRegisteredNodes() {
        return new HashMap<>(registeredNodes);
    }

    /**
     * Gets active nodes only.
     *
     * @return map of active node information
     */
    public Map<String, NodeInfo> getActiveNodes() {
        Map<String, NodeInfo> activeNodes = new HashMap<>();
        for (Map.Entry<String, NodeInfo> entry : registeredNodes.entrySet()) {
            if (entry.getValue().isActive()) {
                activeNodes.put(entry.getKey(), entry.getValue());
            }
        }
        return activeNodes;
    }

    /**
     * Shuts down the network controller gracefully.
     */
    public void shutdown() {
        if (!running) {
            return;
        }

        running = false;
        logger.info("Shutting down network controller");

        // Close server socket
        if (serverSocket != null && !serverSocket.isClosed()) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                logger.debug("Error closing server socket: {}", e.getMessage());
            }
        }

        // Shutdown thread pools
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
        }
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }

        logger.info("Network controller shutdown complete");
    }

    // Getters
    public int getPort() {
        return port;
    }

    public boolean isRunning() {
        return running;
    }

    public long getTotalConnections() {
        return totalConnections.get();
    }

    public long getTotalDataTransferred() {
        return totalDataTransferred.get();
    }
}