package com.cloudsim;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * Represents a file transfer operation in the distributed storage system.
 * A file transfer consists of multiple chunks that can be transferred independently.
 */
public class FileTransfer {
    private final String fileId;
    private final String fileName;
    private final long totalSize;
    private final List<FileChunk> chunks;
    private TransferStatus status;
    private final Instant createdAt;
    private Instant completedAt;

    /**
     * Creates a new file transfer.
     *
     * @param fileId    Unique identifier for the file
     * @param fileName  Name of the file being transferred
     * @param totalSize Total size of the file in bytes
     * @param chunks    List of chunks that make up this file
     */
    public FileTransfer(String fileId, String fileName, long totalSize, List<FileChunk> chunks) {
        this.fileId = fileId;
        this.fileName = fileName;
        this.totalSize = totalSize;
        this.chunks = chunks;
        this.status = TransferStatus.PENDING;
        this.createdAt = Instant.now();
        this.completedAt = null;
    }

    /**
     * Creates a new file transfer with specified creation time.
     *
     * @param fileId    Unique identifier for the file
     * @param fileName  Name of the file being transferred
     * @param totalSize Total size of the file in bytes
     * @param chunks    List of chunks that make up this file
     * @param createdAt When this transfer was created
     */
    public FileTransfer(String fileId, String fileName, long totalSize, List<FileChunk> chunks, Instant createdAt) {
        this.fileId = fileId;
        this.fileName = fileName;
        this.totalSize = totalSize;
        this.chunks = chunks;
        this.status = TransferStatus.PENDING;
        this.createdAt = createdAt;
        this.completedAt = null;
    }

    /**
     * Checks if all chunks in this transfer have been completed.
     *
     * @return true if all chunks are completed, false otherwise
     */
    public boolean isComplete() {
        return chunks.stream().allMatch(chunk -> chunk.getStatus() == TransferStatus.COMPLETED);
    }

    /**
     * Gets the number of completed chunks.
     *
     * @return number of chunks with COMPLETED status
     */
    public long getCompletedChunks() {
        return chunks.stream().mapToLong(chunk -> chunk.getStatus() == TransferStatus.COMPLETED ? 1 : 0).sum();
    }

    /**
     * Gets the transfer progress as a percentage.
     *
     * @return progress percentage (0.0 to 100.0)
     */
    public double getProgressPercentage() {
        if (chunks.isEmpty()) return 0.0;
        return (double) getCompletedChunks() / chunks.size() * 100.0;
    }

    /**
     * Marks the transfer as completed and sets the completion time.
     */
    public void markCompleted() {
        this.status = TransferStatus.COMPLETED;
        this.completedAt = Instant.now();
    }

    /**
     * Gets the transfer duration in milliseconds.
     *
     * @return duration in milliseconds, or -1 if not completed
     */
    public long getDurationMillis() {
        if (completedAt == null) return -1;
        return completedAt.toEpochMilli() - createdAt.toEpochMilli();
    }

    // Getters
    public String getFileId() {
        return fileId;
    }

    public String getFileName() {
        return fileName;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public List<FileChunk> getChunks() {
        return chunks;
    }

    public TransferStatus getStatus() {
        return status;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public Instant getCompletedAt() {
        return completedAt;
    }

    // Setters
    public void setStatus(TransferStatus status) {
        this.status = status;
    }

    public void setCompletedAt(Instant completedAt) {
        this.completedAt = completedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FileTransfer that = (FileTransfer) o;
        return totalSize == that.totalSize &&
                Objects.equals(fileId, that.fileId) &&
                Objects.equals(fileName, that.fileName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileId, fileName, totalSize);
    }

    @Override
    public String toString() {
        return String.format("FileTransfer{id='%s', name='%s', size=%d, status=%s, progress=%.1f%%}",
                fileId, fileName, totalSize, status, getProgressPercentage());
    }
}