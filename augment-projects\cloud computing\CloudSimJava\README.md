# Distributed Cloud Storage Simulation

This project implements a distributed cloud storage system simulation in Java. The goal was to understand how cloud storage services like Amazon S3 or Google Drive work internally by building a simplified version.

## Project Overview

The system simulates a distributed storage network where multiple storage nodes work together to store and manage files. The main components include:

- Storage nodes that can join and leave the network
- A central network controller for coordination
- File chunking and distribution across nodes
- Basic load balancing and fault tolerance
- Resource monitoring and management

## System Architecture

The system consists of several key components:

### Main Components

1. **StorageVirtualNode** - Represents individual storage nodes in the network
2. **StorageVirtualNetwork** - Central controller that manages all nodes
3. **FileTransferOrchestrator** - Handles file distribution logic
4. **CloudSimMain** - Main application entry point

### Supporting Classes

- **FileChunk** - Represents pieces of files for distributed storage
- **FileTransfer** - Tracks file transfer operations
- **NodeCapacity** - Stores resource information for each node
- **NodeInfo** - Contains node registration and status data

## How to Run

### Requirements

- Java 11 or newer
- Maven for building

### Building

```bash
cd CloudSimJava
mvn clean compile
mvn package
```

This creates the JAR file: `target/cloud-storage-simulation-1.0.0.jar`

### Running the System

#### Quick Start

1. First start the network controller:
```bash
java -jar target/cloud-storage-simulation-1.0.0.jar --network
```

2. Then start some storage nodes (in separate command windows):
```bash
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node1
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node2
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id node3
```

#### Custom Configuration

You can also specify custom resources for nodes:
```bash
java -jar target/cloud-storage-simulation-1.0.0.jar --node --node-id bignode \
  --cpu 8 --memory 16 --storage 500 --bandwidth 2000
```

## 📋 Command Line Options

### Network Controller Mode
- `--network` or `-n`: Start as network controller
- `--port` or `-p`: Port number (default: 8080)

### Storage Node Mode
- `--node` or `-s`: Start as storage node
- `--node-id` or `-i`: Unique node identifier (required)
- `--host` or `-H`: Network controller host (default: localhost)
- `--network-port` or `-P`: Network controller port (default: 8080)

### Resource Configuration
- `--cpu` or `-c`: CPU cores (default: 4)
- `--memory` or `-m`: Memory in GB (default: 8)
- `--storage` or `-S`: Storage capacity in GB (default: 100)
- `--bandwidth` or `-b`: Network bandwidth in Mbps (default: 1000)

### Help
- `--help` or `-h`: Show help message

## 🔧 Configuration Examples

### Small Development Setup
```bash
# Terminal 1: Network Controller
java -jar cloud-storage-simulation.jar --network

# Terminal 2: Small node
java -jar cloud-storage-simulation.jar --node --node-id dev1 \
  --cpu 2 --memory 4 --storage 50 --bandwidth 500

# Terminal 3: Another small node
java -jar cloud-storage-simulation.jar --node --node-id dev2 \
  --cpu 2 --memory 4 --storage 50 --bandwidth 500
```

### Production-like Setup
```bash
# Terminal 1: Network Controller on custom port
java -jar cloud-storage-simulation.jar --network --port 8090

# Terminal 2: High-capacity node
java -jar cloud-storage-simulation.jar --node --node-id prod1 \
  --network-port 8090 --cpu 16 --memory 32 --storage 2000 --bandwidth 10000

# Terminal 3: Medium-capacity node
java -jar cloud-storage-simulation.jar --node --node-id prod2 \
  --network-port 8090 --cpu 8 --memory 16 --storage 1000 --bandwidth 5000

# Terminal 4: Backup node
java -jar cloud-storage-simulation.jar --node --node-id backup1 \
  --network-port 8090 --cpu 4 --memory 8 --storage 500 --bandwidth 2000
```

## 🔍 What You'll See

### Network Controller Output
```
2025-08-19 11:00:00 INFO  StorageVirtualNetwork - Created network controller on port 8080
2025-08-19 11:00:00 INFO  StorageVirtualNetwork - Starting network controller on port 8080
2025-08-19 11:00:00 INFO  StorageVirtualNetwork - Network controller listening on port 8080
2025-08-19 11:00:00 INFO  StorageVirtualNetwork - Started heartbeat monitoring
2025-08-19 11:00:05 INFO  StorageVirtualNetwork - Registered node node1 with capacity: NodeCapacity{cpu=4, memory=8GB, storage=100.0GB, bandwidth=1000.0Mbps}
2025-08-19 11:00:10 INFO  StorageVirtualNetwork - Node node1 is now active
```

### Storage Node Output
```
2025-08-19 11:00:05 INFO  StorageVirtualNode - Created storage node node1 with capacity: NodeCapacity{cpu=4, memory=8GB, storage=100.0GB, bandwidth=1000.0Mbps}
2025-08-19 11:00:05 INFO  StorageVirtualNode - Starting storage node node1
2025-08-19 11:00:05 INFO  HeartbeatServer - Heartbeat server for node node1 started on port 5234
2025-08-19 11:00:05 INFO  StorageVirtualNode - Node node1 registered successfully with network controller
2025-08-19 11:00:05 INFO  HeartbeatSender - Heartbeat sender for node node1 started
2025-08-19 11:00:05 INFO  StorageVirtualNode - Storage node node1 started successfully
```

## 🧪 Testing the System

### Manual Testing

1. **Start the network controller** in one terminal
2. **Start 2-3 storage nodes** in separate terminals
3. **Observe the logs** to see:
   - Node registration
   - Heartbeat exchanges
   - Resource reporting
   - Network topology updates

### Programmatic Testing

You can extend the system by adding a test client that:
- Initiates file transfers using `FileTransferOrchestrator`
- Monitors transfer progress
- Validates data integrity
- Tests failure scenarios

## 📊 Key Features Demonstrated

### 1. Distributed Architecture
- **Node Discovery**: Automatic registration and heartbeat monitoring
- **Load Balancing**: Intelligent node selection based on available resources
- **Fault Tolerance**: Heartbeat timeout detection and node status management

### 2. File Management
- **Chunking Strategy**: Adaptive chunk sizes based on file size
- **Replication**: Configurable number of replicas across nodes
- **Transfer Simulation**: Realistic bandwidth constraints and timing

### 3. Resource Management
- **Capacity Tracking**: Real-time monitoring of storage, CPU, memory, and bandwidth
- **Utilization Metrics**: Performance statistics and resource usage
- **Scalability**: Support for adding/removing nodes dynamically

## 🔧 Extending the System

### Adding New Features

1. **Web Dashboard**: Create a REST API and web interface for monitoring
2. **Data Persistence**: Add actual file storage and retrieval
3. **Advanced Load Balancing**: Implement sophisticated node selection algorithms
4. **Failure Simulation**: Add network partitions and node failure scenarios
5. **Performance Benchmarking**: Add throughput and latency measurements

### Code Structure

```
src/main/java/com/cloudsim/
├── CloudSimMain.java              # Application entry point
├── StorageVirtualNode.java        # Storage node implementation
├── StorageVirtualNetwork.java     # Network controller
├── FileTransferOrchestrator.java  # Transfer coordination
├── HeartbeatServer.java          # UDP heartbeat responder
├── HeartbeatSender.java          # Heartbeat client
├── FileChunk.java                # File chunk data model
├── FileTransfer.java             # File transfer data model
├── NodeCapacity.java             # Resource capacity model
├── NodeInfo.java                 # Node information model
├── NodeConnection.java           # Network connection model
└── TransferStatus.java           # Transfer status enumeration
```

## 🎓 Educational Value

This simulation teaches key distributed systems concepts:

- **Network Programming**: Socket communication and protocol design
- **Concurrency**: Multi-threading and asynchronous processing
- **Resource Management**: Capacity planning and utilization tracking
- **Load Balancing**: Node selection and traffic distribution
- **Fault Tolerance**: Heartbeat monitoring and failure detection
- **Data Distribution**: File chunking and replication strategies

## 🚨 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```
   Error: Failed to start network controller: Address already in use
   ```
   Solution: Use a different port with `--port` option

2. **Connection Refused**
   ```
   Error: Failed to start storage node: Connection refused
   ```
   Solution: Ensure network controller is running first

3. **Node Registration Failed**
   ```
   Error: Registration failed: Connection timeout
   ```
   Solution: Check network controller host/port configuration

### Debug Mode

Enable debug logging by adding to your JVM arguments:
```bash
java -Dlogback.configurationFile=logback-debug.xml -jar cloud-storage-simulation.jar
```

## 📝 License

This project is for educational purposes. Feel free to modify and extend it for learning about distributed systems and cloud computing concepts.

## 🤝 Contributing

This is an educational project. Suggestions for improvements and additional features are welcome!

---

**Happy Learning!** 🎉

This simulation provides a hands-on way to understand how distributed cloud storage systems work under the hood. Experiment with different configurations, observe the behavior, and extend it with your own features!