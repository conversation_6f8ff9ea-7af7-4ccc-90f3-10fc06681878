package com.cloudsim;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * UDP server that responds to heartbeat ping requests from the network controller.
 */
public class HeartbeatServer extends Thread {
    private static final Logger logger = LoggerFactory.getLogger(HeartbeatServer.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    private final String nodeId;
    private final int port;
    private volatile boolean running = false;
    private DatagramSocket socket;

    /**
     * Creates a new heartbeat server.
     *
     * @param nodeId Node identifier
     * @param port   Port to bind to (0 for random port)
     */
    public HeartbeatServer(String nodeId, int port) {
        super("HeartbeatServer-" + nodeId);
        this.nodeId = nodeId;
        this.port = port != 0 ? port : new Random().nextInt(4000) + 5001;
        setDaemon(true);
    }

    /**
     * Gets the actual port this server is bound to.
     *
     * @return port number
     */
    public int getPort() {
        return socket != null ? socket.getLocalPort() : port;
    }

    @Override
    public void run() {
        running = true;

        try {
            socket = new DatagramSocket(port);
            logger.info("Heartbeat server for node {} started on port {}", nodeId, socket.getLocalPort());

            byte[] buffer = new byte[1024];

            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
                    socket.receive(packet);

                    String message = new String(packet.getData(), 0, packet.getLength());

                    if ("PING".equals(message)) {
                        // Send response
                        Map<String, Object> response = new HashMap<>();
                        response.put("node_id", nodeId);
                        response.put("status", "ALIVE");

                        byte[] responseData = objectMapper.writeValueAsBytes(response);
                        DatagramPacket responsePacket = new DatagramPacket(
                                responseData, responseData.length,
                                packet.getAddress(), packet.getPort()
                        );

                        socket.send(responsePacket);
                    }

                } catch (SocketTimeoutException e) {
                    // Timeout is normal, continue
                } catch (IOException e) {
                    if (running) {
                        logger.error("Error in heartbeat server for node {}: {}", nodeId, e.getMessage());
                    }
                }
            }

        } catch (SocketException e) {
            logger.error("Failed to start heartbeat server for node {}: {}", nodeId, e.getMessage());
        } finally {
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
            logger.info("Heartbeat server for node {} stopped", nodeId);
        }
    }

    /**
     * Stops the heartbeat server.
     */
    public void stopServer() {
        running = false;
        if (socket != null && !socket.isClosed()) {
            socket.close();
        }
        interrupt();
    }
}