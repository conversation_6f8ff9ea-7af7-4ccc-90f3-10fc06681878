package com.cloudsim;

import java.time.Instant;
import java.util.Objects;

/**
 * Information about a registered storage node in the network.
 */
public class NodeInfo {
    private final String nodeId;
    private final String host;
    private final int port;
    private final NodeCapacity capacity;
    private final Instant registeredAt;
    private Instant lastSeen;
    private boolean active;

    /**
     * Creates new node information.
     *
     * @param nodeId   Unique node identifier
     * @param host     Host address of the node
     * @param port     Port of the node
     * @param capacity Resource capacity of the node
     */
    public NodeInfo(String nodeId, String host, int port, NodeCapacity capacity) {
        this.nodeId = nodeId;
        this.host = host;
        this.port = port;
        this.capacity = capacity;
        this.registeredAt = Instant.now();
        this.lastSeen = Instant.now();
        this.active = true;
    }

    /**
     * Updates the last seen timestamp.
     */
    public void updateLastSeen() {
        this.lastSeen = Instant.now();
    }

    // Getters
    public String getNodeId() {
        return nodeId;
    }

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public NodeCapacity getCapacity() {
        return capacity;
    }

    public Instant getRegisteredAt() {
        return registeredAt;
    }

    public Instant getLastSeen() {
        return lastSeen;
    }

    public boolean isActive() {
        return active;
    }

    // Setters
    public void setLastSeen(Instant lastSeen) {
        this.lastSeen = lastSeen;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NodeInfo nodeInfo = (NodeInfo) o;
        return port == nodeInfo.port &&
                Objects.equals(nodeId, nodeInfo.nodeId) &&
                Objects.equals(host, nodeInfo.host);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nodeId, host, port);
    }

    @Override
    public String toString() {
        return String.format("NodeInfo{id='%s', host='%s', port=%d, active=%s, capacity=%s}",
                nodeId, host, port, active, capacity);
    }
}