package com.cloudsim;

import java.util.Objects;

/**
 * Represents the resource capacity of a storage node.
 */
public class NodeCapacity {
    private final int cpu;           // CPU cores
    private final int memory;        // Memory in GB
    private final long storage;      // Storage in bytes
    private final long bandwidth;    // Bandwidth in bits per second

    /**
     * Creates a new node capacity specification.
     *
     * @param cpu       Number of CPU cores
     * @param memory    Memory capacity in GB
     * @param storage   Storage capacity in bytes
     * @param bandwidth Network bandwidth in bits per second
     */
    public NodeCapacity(int cpu, int memory, long storage, long bandwidth) {
        this.cpu = cpu;
        this.memory = memory;
        this.storage = storage;
        this.bandwidth = bandwidth;
    }

    // Getters
    public int getCpu() {
        return cpu;
    }

    public int getMemory() {
        return memory;
    }

    public long getStorage() {
        return storage;
    }

    public long getBandwidth() {
        return bandwidth;
    }

    /**
     * Gets storage capacity in GB for display purposes.
     *
     * @return storage capacity in GB
     */
    public double getStorageGB() {
        return storage / (1024.0 * 1024.0 * 1024.0);
    }

    /**
     * Gets bandwidth in Mbps for display purposes.
     *
     * @return bandwidth in Mbps
     */
    public double getBandwidthMbps() {
        return bandwidth / 1_000_000.0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NodeCapacity that = (NodeCapacity) o;
        return cpu == that.cpu &&
                memory == that.memory &&
                storage == that.storage &&
                bandwidth == that.bandwidth;
    }

    @Override
    public int hashCode() {
        return Objects.hash(cpu, memory, storage, bandwidth);
    }

    @Override
    public String toString() {
        return String.format("NodeCapacity{cpu=%d, memory=%dGB, storage=%.1fGB, bandwidth=%.1fMbps}",
                cpu, memory, getStorageGB(), getBandwidthMbps());
    }
}