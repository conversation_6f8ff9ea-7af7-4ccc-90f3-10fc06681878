package com.cloudsim;

import java.util.Objects;

/**
 * Represents a chunk of a file in the distributed storage system.
 * Files are broken into chunks for efficient transfer and storage across nodes.
 */
public class FileChunk {
    private final int chunkId;
    private final int size;
    private final String checksum;
    private TransferStatus status;
    private String storedNode;

    /**
     * Creates a new file chunk.
     *
     * @param chunkId  Unique identifier for this chunk within the file
     * @param size     Size of the chunk in bytes
     * @param checksum MD5 checksum for data integrity verification
     */
    public FileChunk(int chunkId, int size, String checksum) {
        this.chunkId = chunkId;
        this.size = size;
        this.checksum = checksum;
        this.status = TransferStatus.PENDING;
        this.storedNode = null;
    }

    /**
     * Creates a new file chunk with specified status and storage node.
     *
     * @param chunkId    Unique identifier for this chunk within the file
     * @param size       Size of the chunk in bytes
     * @param checksum   MD5 checksum for data integrity verification
     * @param status     Current transfer status
     * @param storedNode Node ID where this chunk is stored (null if not stored)
     */
    public FileChunk(int chunkId, int size, String checksum, TransferStatus status, String storedNode) {
        this.chunkId = chunkId;
        this.size = size;
        this.checksum = checksum;
        this.status = status;
        this.storedNode = storedNode;
    }

    // Getters
    public int getChunkId() {
        return chunkId;
    }

    public int getSize() {
        return size;
    }

    public String getChecksum() {
        return checksum;
    }

    public TransferStatus getStatus() {
        return status;
    }

    public String getStoredNode() {
        return storedNode;
    }

    // Setters
    public void setStatus(TransferStatus status) {
        this.status = status;
    }

    public void setStoredNode(String storedNode) {
        this.storedNode = storedNode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FileChunk fileChunk = (FileChunk) o;
        return chunkId == fileChunk.chunkId &&
                size == fileChunk.size &&
                Objects.equals(checksum, fileChunk.checksum);
    }

    @Override
    public int hashCode() {
        return Objects.hash(chunkId, size, checksum);
    }

    @Override
    public String toString() {
        return String.format("FileChunk{id=%d, size=%d, status=%s, node=%s}",
                chunkId, size, status, storedNode);
    }
}