package com.cloudsim;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This class handles file transfers between storage nodes.
 * I created this to manage how files get distributed across the network.
 * It includes basic load balancing to choose which nodes should store files.
 */
public class FileTransferOrchestrator {
    private static final Logger logger = LoggerFactory.getLogger(FileTransferOrchestrator.class);

    private final StorageVirtualNetwork networkController;
    private final ExecutorService executorService;
    private final Map<String, FileTransfer> activeTransfers = new ConcurrentHashMap<>();
    private final Random random = new Random();

    /**
     * Creates a new file transfer orchestrator.
     *
     * @param networkController Network controller for node management
     */
    public FileTransferOrchestrator(StorageVirtualNetwork networkController) {
        this.networkController = networkController;
        this.executorService = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "FileTransferOrchestrator-Worker");
            t.setDaemon(true);
            return t;
        });
    }

    /**
     * Initiates a file transfer to the distributed storage system.
     *
     * @param fileName     Name of the file to transfer
     * @param fileSize     Size of the file in bytes
     * @param replication  Number of replicas to create (default: 2)
     * @return FileTransfer object if successful, null if failed
     */
    public FileTransfer initiateFileTransfer(String fileName, long fileSize, int replication) {
        String fileId = generateFileId(fileName);

        // Get available nodes
        Map<String, NodeInfo> activeNodes = networkController.getActiveNodes();
        if (activeNodes.isEmpty()) {
            logger.error("No active nodes available for file transfer");
            return null;
        }

        // Select target nodes for storage
        List<String> targetNodes = selectTargetNodes(activeNodes, replication, fileSize);
        if (targetNodes.isEmpty()) {
            logger.error("No suitable nodes found for file transfer");
            return null;
        }

        logger.info("Initiating file transfer for {} ({} bytes) to {} nodes: {}",
                fileName, fileSize, targetNodes.size(), targetNodes);

        // Create file transfer
        List<FileChunk> chunks = generateChunks(fileId, fileSize);
        FileTransfer transfer = new FileTransfer(fileId, fileName, fileSize, chunks);
        activeTransfers.put(fileId, transfer);

        // Start transfer process
        executorService.submit(() -> executeFileTransfer(transfer, targetNodes));

        return transfer;
    }

    /**
     * Selects target nodes for file storage based on available capacity and load balancing.
     *
     * @param activeNodes Available active nodes
     * @param replication Number of replicas needed
     * @param fileSize    Size of the file
     * @return list of selected node IDs
     */
    private List<String> selectTargetNodes(Map<String, NodeInfo> activeNodes, int replication, long fileSize) {
        // Filter nodes with sufficient storage capacity
        List<NodeInfo> suitableNodes = activeNodes.values().stream()
                .filter(node -> node.getCapacity().getStorage() >= fileSize)
                .collect(Collectors.toList());

        if (suitableNodes.isEmpty()) {
            return Collections.emptyList();
        }

        // Sort by available storage (descending) for load balancing
        suitableNodes.sort((a, b) -> Long.compare(
                b.getCapacity().getStorage(),
                a.getCapacity().getStorage()
        ));

        // Select up to 'replication' nodes, but not more than available
        int numNodes = Math.min(replication, suitableNodes.size());
        List<String> selectedNodes = new ArrayList<>();

        for (int i = 0; i < numNodes; i++) {
            selectedNodes.add(suitableNodes.get(i).getNodeId());
        }

        return selectedNodes;
    }

    /**
     * Executes the actual file transfer to target nodes.
     *
     * @param transfer    File transfer object
     * @param targetNodes List of target node IDs
     */
    private void executeFileTransfer(FileTransfer transfer, List<String> targetNodes) {
        transfer.setStatus(TransferStatus.IN_PROGRESS);
        logger.info("Starting file transfer execution for {}", transfer.getFileName());

        try {
            // Simulate transfer to each target node
            List<CompletableFuture<Boolean>> transferFutures = new ArrayList<>();

            for (String nodeId : targetNodes) {
                CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() ->
                        simulateNodeTransfer(transfer, nodeId), executorService);
                transferFutures.add(future);
            }

            // Wait for all transfers to complete
            CompletableFuture<Void> allTransfers = CompletableFuture.allOf(
                    transferFutures.toArray(new CompletableFuture[0]));

            allTransfers.get(30, TimeUnit.SECONDS); // 30 second timeout

            // Check results
            boolean allSuccessful = transferFutures.stream()
                    .allMatch(future -> {
                        try {
                            return future.get();
                        } catch (Exception e) {
                            return false;
                        }
                    });

            if (allSuccessful) {
                transfer.markCompleted();
                logger.info("File transfer completed successfully for {}", transfer.getFileName());
            } else {
                transfer.setStatus(TransferStatus.FAILED);
                logger.error("File transfer failed for {}", transfer.getFileName());
            }

        } catch (Exception e) {
            transfer.setStatus(TransferStatus.FAILED);
            logger.error("File transfer execution failed for {}: {}", transfer.getFileName(), e.getMessage(), e);
        } finally {
            activeTransfers.remove(transfer.getFileId());
        }
    }

    /**
     * Simulates transferring a file to a specific storage node.
     * This method simulates the time it would take to transfer each chunk
     * based on network bandwidth limitations.
     */
    private boolean simulateNodeTransfer(FileTransfer transfer, String nodeId) {
        logger.debug("Simulating transfer of {} to node {}", transfer.getFileName(), nodeId);

        try {
            // Go through each chunk and simulate the transfer time
            for (FileChunk chunk : transfer.getChunks()) {
                // Calculate transfer time based on chunk size and network speed
                long chunkSizeBits = chunk.getSize() * 8L;
                double transferTimeMs = (double) chunkSizeBits / (100 * 1_000_000) * 1000; // Assuming 100Mbps network

                // Add some randomness to simulate real network conditions
                transferTimeMs *= (0.8 + random.nextDouble() * 0.4); // ±20% variation

                Thread.sleep((long) transferTimeMs);

                // Mark chunk as completed
                chunk.setStatus(TransferStatus.COMPLETED);
                chunk.setStoredNode(nodeId);

                logger.debug("Transferred chunk {} of {} to node {}",
                        chunk.getChunkId(), transfer.getFileName(), nodeId);
            }

            logger.info("Successfully transferred {} to node {}", transfer.getFileName(), nodeId);
            return true;

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.warn("Transfer of {} to node {} was interrupted", transfer.getFileName(), nodeId);
            return false;
        } catch (Exception e) {
            logger.error("Failed to transfer {} to node {}: {}",
                    transfer.getFileName(), nodeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Generates a unique file ID based on the file name and timestamp.
     *
     * @param fileName Original file name
     * @return unique file ID
     */
    private String generateFileId(String fileName) {
        return String.format("%s-%d-%d",
                fileName.replaceAll("[^a-zA-Z0-9.-]", "_"),
                System.currentTimeMillis(),
                random.nextInt(10000));
    }

    /**
     * Generates file chunks for a given file.
     *
     * @param fileId   Unique file identifier
     * @param fileSize Size of the file in bytes
     * @return list of file chunks
     */
    private List<FileChunk> generateChunks(String fileId, long fileSize) {
        int chunkSize = calculateChunkSize(fileSize);
        int numChunks = (int) Math.ceil((double) fileSize / chunkSize);

        List<FileChunk> chunks = new ArrayList<>();
        for (int i = 0; i < numChunks; i++) {
            String checksum = generateChecksum(fileId + "-" + i);
            long actualChunkSize = Math.min(chunkSize, fileSize - (long) i * chunkSize);
            chunks.add(new FileChunk(i, (int) actualChunkSize, checksum));
        }

        return chunks;
    }

    /**
     * Calculates optimal chunk size based on file size.
     *
     * @param fileSize Size of the file in bytes
     * @return optimal chunk size in bytes
     */
    private int calculateChunkSize(long fileSize) {
        if (fileSize < 10 * 1024 * 1024) {  // < 10MB
            return 512 * 1024;  // 512KB chunks
        } else if (fileSize < 100 * 1024 * 1024) {  // < 100MB
            return 2 * 1024 * 1024;  // 2MB chunks
        } else {
            return 10 * 1024 * 1024;  // 10MB chunks
        }
    }

    /**
     * Generates a simple checksum for simulation purposes.
     *
     * @param input Input string to hash
     * @return checksum as hex string
     */
    private String generateChecksum(String input) {
        return String.valueOf(Math.abs(input.hashCode()));
    }

    /**
     * Gets all active file transfers.
     *
     * @return map of active transfers
     */
    public Map<String, FileTransfer> getActiveTransfers() {
        return new ConcurrentHashMap<>(activeTransfers);
    }

    /**
     * Gets transfer statistics.
     *
     * @return map containing transfer statistics
     */
    public Map<String, Object> getTransferStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("active_transfers", activeTransfers.size());

        long totalSize = activeTransfers.values().stream()
                .mapToLong(FileTransfer::getTotalSize)
                .sum();
        stats.put("total_active_size", totalSize);

        return stats;
    }

    /**
     * Shuts down the file transfer orchestrator.
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        logger.info("File transfer orchestrator shutdown complete");
    }
}