package com.cloudsim;

import java.util.Objects;

/**
 * Represents a network connection between two storage nodes.
 */
public class NodeConnection {
    private final String nodeId;
    private final String host;
    private final int port;
    private final long bandwidth; // bits per second

    /**
     * Creates a new node connection.
     *
     * @param nodeId    ID of the connected node
     * @param host      Host address of the connected node
     * @param port      Port of the connected node
     * @param bandwidth Available bandwidth in bits per second
     */
    public NodeConnection(String nodeId, String host, int port, long bandwidth) {
        this.nodeId = nodeId;
        this.host = host;
        this.port = port;
        this.bandwidth = bandwidth;
    }

    // Getters
    public String getNodeId() {
        return nodeId;
    }

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public long getBandwidth() {
        return bandwidth;
    }

    /**
     * Gets bandwidth in Mbps for display purposes.
     *
     * @return bandwidth in Mbps
     */
    public double getBandwidthMbps() {
        return bandwidth / 1_000_000.0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NodeConnection that = (NodeConnection) o;
        return port == that.port &&
                bandwidth == that.bandwidth &&
                Objects.equals(nodeId, that.nodeId) &&
                Objects.equals(host, that.host);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nodeId, host, port, bandwidth);
    }

    @Override
    public String toString() {
        return String.format("NodeConnection{id='%s', host='%s', port=%d, bandwidth=%.1fMbps}",
                nodeId, host, port, getBandwidthMbps());
    }
}