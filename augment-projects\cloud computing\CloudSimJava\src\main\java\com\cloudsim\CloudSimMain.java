package com.cloudsim;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.CommandLineParser;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.Option;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Main entry point for the Cloud Storage Simulation application.
 * Supports launching either a network controller or storage nodes.
 */
public class CloudSimMain {
    private static final Logger logger = LoggerFactory.getLogger(CloudSimMain.class);

    // Default configuration values
    private static final int DEFAULT_NETWORK_PORT = 8080;
    private static final String DEFAULT_NETWORK_HOST = "localhost";
    private static final int DEFAULT_CPU = 4;
    private static final int DEFAULT_MEMORY = 8; // GB
    private static final long DEFAULT_STORAGE = 100L * 1024 * 1024 * 1024; // 100GB
    private static final long DEFAULT_BANDWIDTH = 1000L * 1000 * 1000; // 1Gbps

    public static void main(String[] args) {
        Options options = createOptions();
        CommandLineParser parser = new DefaultParser();

        try {
            CommandLine cmd = parser.parse(options, args);

            if (cmd.hasOption("help")) {
                printHelp(options);
                return;
            }

            if (cmd.hasOption("network")) {
                startNetworkController(cmd);
            } else if (cmd.hasOption("node")) {
                startStorageNode(cmd);
            } else {
                System.err.println("Error: Must specify either --network or --node");
                printHelp(options);
                System.exit(1);
            }

        } catch (ParseException e) {
            System.err.println("Error parsing command line: " + e.getMessage());
            printHelp(options);
            System.exit(1);
        } catch (Exception e) {
            logger.error("Application error: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    /**
     * Creates command line options.
     *
     * @return Options object with all available command line options
     */
    private static Options createOptions() {
        Options options = new Options();

        // Mode selection
        options.addOption("n", "network", false, "Start as network controller");
        options.addOption("s", "node", false, "Start as storage node");
        options.addOption("h", "help", false, "Show help message");

        // Network controller options
        options.addOption(Option.builder("p")
                .longOpt("port")
                .hasArg()
                .argName("PORT")
                .desc("Port for network controller (default: " + DEFAULT_NETWORK_PORT + ")")
                .build());

        // Storage node options
        options.addOption(Option.builder("i")
                .longOpt("node-id")
                .hasArg()
                .argName("ID")
                .desc("Unique identifier for the storage node")
                .build());

        options.addOption(Option.builder("H")
                .longOpt("host")
                .hasArg()
                .argName("HOST")
                .desc("Network controller host (default: " + DEFAULT_NETWORK_HOST + ")")
                .build());

        options.addOption(Option.builder("P")
                .longOpt("network-port")
                .hasArg()
                .argName("PORT")
                .desc("Network controller port (default: " + DEFAULT_NETWORK_PORT + ")")
                .build());

        // Resource configuration
        options.addOption(Option.builder("c")
                .longOpt("cpu")
                .hasArg()
                .argName("CORES")
                .desc("Number of CPU cores (default: " + DEFAULT_CPU + ")")
                .build());

        options.addOption(Option.builder("m")
                .longOpt("memory")
                .hasArg()
                .argName("GB")
                .desc("Memory in GB (default: " + DEFAULT_MEMORY + ")")
                .build());

        options.addOption(Option.builder("S")
                .longOpt("storage")
                .hasArg()
                .argName("GB")
                .desc("Storage capacity in GB (default: " + (DEFAULT_STORAGE / (1024*1024*1024)) + ")")
                .build());

        options.addOption(Option.builder("b")
                .longOpt("bandwidth")
                .hasArg()
                .argName("MBPS")
                .desc("Network bandwidth in Mbps (default: " + (DEFAULT_BANDWIDTH / 1_000_000) + ")")
                .build());

        return options;
    }

    /**
     * Starts the network controller.
     *
     * @param cmd Command line arguments
     */
    private static void startNetworkController(CommandLine cmd) {
        int port = DEFAULT_NETWORK_PORT;
        if (cmd.hasOption("port")) {
            try {
                port = Integer.parseInt(cmd.getOptionValue("port"));
            } catch (NumberFormatException e) {
                System.err.println("Error: Invalid port number");
                System.exit(1);
            }
        }

        logger.info("Starting Cloud Storage Network Controller on port {}", port);

        StorageVirtualNetwork network = new StorageVirtualNetwork(port);

        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutting down network controller...");
            network.shutdown();
        }));

        try {
            network.start();
        } catch (Exception e) {
            logger.error("Failed to start network controller: {}", e.getMessage(), e);
            System.exit(1);
        }
    }

    /**
     * Starts a storage node.
     *
     * @param cmd Command line arguments
     */
    private static void startStorageNode(CommandLine cmd) {
        // Validate required parameters
        if (!cmd.hasOption("node-id")) {
            System.err.println("Error: --node-id is required for storage nodes");
            System.exit(1);
        }

        String nodeId = cmd.getOptionValue("node-id");
        String networkHost = cmd.getOptionValue("host", DEFAULT_NETWORK_HOST);
        int networkPort = DEFAULT_NETWORK_PORT;

        if (cmd.hasOption("network-port")) {
            try {
                networkPort = Integer.parseInt(cmd.getOptionValue("network-port"));
            } catch (NumberFormatException e) {
                System.err.println("Error: Invalid network port number");
                System.exit(1);
            }
        }

        // Parse resource configuration
        int cpu = DEFAULT_CPU;
        int memory = DEFAULT_MEMORY;
        long storage = DEFAULT_STORAGE;
        long bandwidth = DEFAULT_BANDWIDTH;

        try {
            if (cmd.hasOption("cpu")) {
                cpu = Integer.parseInt(cmd.getOptionValue("cpu"));
            }
            if (cmd.hasOption("memory")) {
                memory = Integer.parseInt(cmd.getOptionValue("memory"));
            }
            if (cmd.hasOption("storage")) {
                long storageGB = Long.parseLong(cmd.getOptionValue("storage"));
                storage = storageGB * 1024 * 1024 * 1024; // Convert GB to bytes
            }
            if (cmd.hasOption("bandwidth")) {
                long bandwidthMbps = Long.parseLong(cmd.getOptionValue("bandwidth"));
                bandwidth = bandwidthMbps * 1_000_000; // Convert Mbps to bps
            }
        } catch (NumberFormatException e) {
            System.err.println("Error: Invalid resource configuration");
            System.exit(1);
        }

        NodeCapacity capacity = new NodeCapacity(cpu, memory, storage, bandwidth);

        logger.info("Starting Storage Node {} with capacity: {}", nodeId, capacity);
        logger.info("Connecting to network controller at {}:{}", networkHost, networkPort);

        StorageVirtualNode node = new StorageVirtualNode(nodeId, capacity, networkHost, networkPort);

        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            logger.info("Shutting down storage node {}...", nodeId);
            node.shutdown();
        }));

        try {
            node.start();

            // Keep the application running
            Thread.currentThread().join();

        } catch (Exception e) {
            logger.error("Failed to start storage node {}: {}", nodeId, e.getMessage(), e);
            System.exit(1);
        }
    }

    /**
     * Prints help message.
     *
     * @param options Command line options
     */
    private static void printHelp(Options options) {
        System.out.println("Cloud Storage Simulation - Java Version");
        System.out.println("========================================");
        System.out.println();
        System.out.println("Usage:");
        System.out.println("  java -jar cloud-storage-simulation.jar [OPTIONS]");
        System.out.println();
        System.out.println("Examples:");
        System.out.println("  # Start network controller on default port (8080)");
        System.out.println("  java -jar cloud-storage-simulation.jar --network");
        System.out.println();
        System.out.println("  # Start network controller on custom port");
        System.out.println("  java -jar cloud-storage-simulation.jar --network --port 9000");
        System.out.println();
        System.out.println("  # Start storage node with default resources");
        System.out.println("  java -jar cloud-storage-simulation.jar --node --node-id node1");
        System.out.println();
        System.out.println("  # Start storage node with custom resources");
        System.out.println("  java -jar cloud-storage-simulation.jar --node --node-id node2 \\");
        System.out.println("    --cpu 8 --memory 16 --storage 500 --bandwidth 2000");
        System.out.println();
        System.out.println("Options:");

        // Print each option with description
        for (Option option : options.getOptions()) {
            String shortOpt = option.getOpt() != null ? "-" + option.getOpt() : "";
            String longOpt = option.getLongOpt() != null ? "--" + option.getLongOpt() : "";
            String argName = option.getArgName() != null ? " <" + option.getArgName() + ">" : "";
            String desc = option.getDescription() != null ? option.getDescription() : "";

            System.out.printf("  %-4s %-20s %s%n", shortOpt, longOpt + argName, desc);
        }

        System.out.println();
        System.out.println("Notes:");
        System.out.println("  - Start the network controller first, then start storage nodes");
        System.out.println("  - Each storage node must have a unique node-id");
        System.out.println("  - Storage capacity is specified in GB, bandwidth in Mbps");
        System.out.println("  - Use Ctrl+C to gracefully shutdown");
    }
}