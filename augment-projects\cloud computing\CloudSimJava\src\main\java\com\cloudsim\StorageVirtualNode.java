package com.cloudsim;

import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.net.Socket;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicLong;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * This class represents a storage node in our distributed system.
 * Each node can store files and communicate with other nodes and the network controller.
 *
 * I implemented this to simulate how real storage servers work in cloud systems.
 * The node tracks its resources (CPU, memory, storage, bandwidth) and can handle
 * file transfers while maintaining heartbeat communication with the controller.
 */
public class StorageVirtualNode {
    private static final Logger logger = LoggerFactory.getLogger(StorageVirtualNode.class);

    // Node identification and configuration
    private final String nodeId;
    private final NodeCapacity capacity;
    private final String networkHost;
    private final int networkPort;

    // Resource tracking
    private final AtomicLong usedStorage = new AtomicLong(0);
    private final AtomicLong networkUtilization = new AtomicLong(0);
    private final Map<String, FileTransfer> activeTransfers = new ConcurrentHashMap<>();
    private final Map<String, FileTransfer> storedFiles = new ConcurrentHashMap<>();
    private final Map<String, NodeConnection> connections = new ConcurrentHashMap<>();

    // Performance metrics
    private final AtomicLong totalRequestsProcessed = new AtomicLong(0);
    private final AtomicLong totalDataTransferred = new AtomicLong(0);
    private final AtomicLong failedTransfers = new AtomicLong(0);

    // Threading and networking
    private final ExecutorService executorService;
    private final ScheduledExecutorService scheduledExecutor;
    private HeartbeatServer heartbeatServer;
    private HeartbeatSender heartbeatSender;
    private volatile boolean running = false;

    /**
     * Creates a new storage virtual node.
     *
     * @param nodeId      Unique identifier for this node
     * @param capacity    Resource capacity specification
     * @param networkHost Host address of the network controller
     * @param networkPort Port of the network controller
     */
    public StorageVirtualNode(String nodeId, NodeCapacity capacity, String networkHost, int networkPort) {
        this.nodeId = nodeId;
        this.capacity = capacity;
        this.networkHost = networkHost;
        this.networkPort = networkPort;

        // Initialize thread pools
        this.executorService = Executors.newCachedThreadPool(r -> {
            Thread t = new Thread(r, "StorageNode-" + nodeId + "-Worker");
            t.setDaemon(true);
            return t;
        });
        this.scheduledExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread t = new Thread(r, "StorageNode-" + nodeId + "-Scheduler");
            t.setDaemon(true);
            return t;
        });

        logger.info("Created storage node {} with capacity: {}", nodeId, capacity);
    }

    /**
     * Starts the storage node and connects to the network controller.
     * This method initializes all the networking components and registers with the controller.
     */
    public void start() {
        if (running) {
            logger.warn("Node {} is already running", nodeId);
            return;
        }

        running = true;
        logger.info("Starting storage node {}", nodeId);

        try {
            // Start heartbeat server
            startHeartbeatServer();

            // Register with network controller
            registerWithNetwork();

            // Start heartbeat sender
            startHeartbeatSender();

            // Send active notification
            notifyActive();

            logger.info("Storage node {} started successfully", nodeId);

        } catch (Exception e) {
            logger.error("Failed to start storage node {}: {}", nodeId, e.getMessage(), e);
            shutdown();
            throw new RuntimeException("Failed to start storage node", e);
        }
    }

    /**
     * Starts the heartbeat server.
     */
    private void startHeartbeatServer() {
        heartbeatServer = new HeartbeatServer(nodeId, 0);
        heartbeatServer.start();
    }

    /**
     * Starts the heartbeat sender.
     */
    private void startHeartbeatSender() {
        heartbeatSender = new HeartbeatSender(nodeId, networkHost, networkPort);
        heartbeatSender.start();
    }

    /**
     * Registers this node with the network controller.
     */
    private void registerWithNetwork() throws IOException, ClassNotFoundException {
        try (Socket socket = new Socket(networkHost, networkPort)) {
            socket.setSoTimeout(5000); // 5 second timeout

            Map<String, Object> message = new HashMap<>();
            message.put("action", "REGISTER");
            message.put("node_id", nodeId);
            message.put("host", "localhost");
            message.put("port", heartbeatServer.getPort());

            Map<String, Object> capacityMap = new HashMap<>();
            capacityMap.put("cpu", capacity.getCpu());
            capacityMap.put("memory", capacity.getMemory());
            capacityMap.put("storage", capacity.getStorage());
            capacityMap.put("bandwidth", capacity.getBandwidth());
            message.put("capacity", capacityMap);

            // Send registration message
            try (ObjectOutputStream out = new ObjectOutputStream(socket.getOutputStream())) {
                out.writeObject(message);
                out.flush();

                // Read response
                try (ObjectInputStream in = new ObjectInputStream(socket.getInputStream())) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> response = (Map<String, Object>) in.readObject();

                    if (!"OK".equals(response.get("status"))) {
                        throw new RuntimeException("Registration failed: " + response);
                    }

                    logger.info("Node {} registered successfully with network controller", nodeId);
                }
            }
        }
    }

    /**
     * Sends an active notification to the network controller.
     */
    private void notifyActive() {
        try (Socket socket = new Socket(networkHost, networkPort)) {
            socket.setSoTimeout(3000); // 3 second timeout

            Map<String, Object> message = new HashMap<>();
            message.put("action", "ACTIVE_NOTIFICATION");
            message.put("node_id", nodeId);

            // Send message
            try (ObjectOutputStream out = new ObjectOutputStream(socket.getOutputStream())) {
                out.writeObject(message);
                out.flush();

                // Read response
                try (ObjectInputStream in = new ObjectInputStream(socket.getInputStream())) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> response = (Map<String, Object>) in.readObject();

                    if (!"ACK".equals(response.get("status"))) {
                        logger.warn("Active notification for node {} not acknowledged", nodeId);
                    }
                }
            }

        } catch (Exception e) {
            logger.warn("Failed to send active notification for node {}: {}", nodeId, e.getMessage());
        }
    }

    /**
     * Adds a connection to another node.
     *
     * @param nodeId    ID of the peer node
     * @param host      Host address of the peer node
     * @param port      Port of the peer node
     * @param bandwidth Available bandwidth in bits per second
     */
    public void addConnection(String nodeId, String host, int port, long bandwidth) {
        connections.put(nodeId, new NodeConnection(nodeId, host, port, bandwidth));
        logger.info("Added connection to node {} ({}:{}) with bandwidth {}Mbps",
                nodeId, host, port, bandwidth / 1_000_000.0);
    }

    /**
     * Calculates optimal chunk size based on file size.
     *
     * @param fileSize Size of the file in bytes
     * @return optimal chunk size in bytes
     */
    private int calculateChunkSize(long fileSize) {
        if (fileSize < 10 * 1024 * 1024) {  // < 10MB
            return 512 * 1024;  // 512KB chunks
        } else if (fileSize < 100 * 1024 * 1024) {  // < 100MB
            return 2 * 1024 * 1024;  // 2MB chunks
        } else {
            return 10 * 1024 * 1024;  // 10MB chunks
        }
    }

    /**
     * Generates file chunks for a given file.
     *
     * @param fileId   Unique file identifier
     * @param fileSize Size of the file in bytes
     * @return list of file chunks
     */
    private List<FileChunk> generateChunks(String fileId, long fileSize) {
        int chunkSize = calculateChunkSize(fileSize);
        int numChunks = (int) Math.ceil((double) fileSize / chunkSize);

        List<FileChunk> chunks = new ArrayList<>();
        for (int i = 0; i < numChunks; i++) {
            String fakeChecksum = generateChecksum(fileId + "-" + i);
            long actualChunkSize = Math.min(chunkSize, fileSize - (long) i * chunkSize);
            chunks.add(new FileChunk(i, (int) actualChunkSize, fakeChecksum));
        }

        return chunks;
    }

    /**
     * Generates a fake MD5 checksum for simulation purposes.
     *
     * @param input Input string to hash
     * @return MD5 hash as hex string
     */
    private String generateChecksum(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            // Fallback to simple hash
            return String.valueOf(input.hashCode());
        }
    }

    /**
     * Initiates a file transfer to this node.
     *
     * @param fileId     Unique file identifier
     * @param fileName   Name of the file
     * @param fileSize   Size of the file in bytes
     * @param sourceNode Source node ID (optional)
     * @return FileTransfer object if successful, null if insufficient storage
     */
    public FileTransfer initiateFileTransfer(String fileId, String fileName, long fileSize, String sourceNode) {
        if (usedStorage.get() + fileSize > capacity.getStorage()) {
            logger.warn("Insufficient storage for file {} on node {}", fileId, nodeId);
            return null;
        }

        List<FileChunk> chunks = generateChunks(fileId, fileSize);
        FileTransfer transfer = new FileTransfer(fileId, fileName, fileSize, chunks);

        activeTransfers.put(fileId, transfer);
        logger.info("Initiated file transfer for {} ({} bytes, {} chunks) on node {}",
                fileName, fileSize, chunks.size(), nodeId);

        return transfer;
    }

    /**
     * Processes a chunk transfer from another node.
     *
     * @param fileId     File identifier
     * @param chunkId    Chunk identifier
     * @param sourceNode Source node identifier
     * @return true if successful, false otherwise
     */
    public boolean processChunkTransfer(String fileId, int chunkId, String sourceNode) {
        FileTransfer transfer = activeTransfers.get(fileId);
        if (transfer == null) {
            logger.warn("No active transfer found for file {} on node {}", fileId, nodeId);
            return false;
        }

        FileChunk chunk = transfer.getChunks().stream()
                .filter(c -> c.getChunkId() == chunkId)
                .findFirst()
                .orElse(null);

        if (chunk == null) {
            logger.warn("Chunk {} not found for file {} on node {}", chunkId, fileId, nodeId);
            return false;
        }

        // Simulate network transfer with bandwidth constraints
        NodeConnection connection = connections.get(sourceNode);
        if (connection == null) {
            logger.warn("No connection to source node {} for chunk transfer", sourceNode);
            return false;
        }

        long chunkSizeBits = chunk.getSize() * 8L;
        long availableBandwidth = Math.min(
                capacity.getBandwidth() - networkUtilization.get(),
                connection.getBandwidth()
        );

        if (availableBandwidth <= 0) {
            logger.debug("No available bandwidth for chunk transfer from {}", sourceNode);
            return false;
        }

        // Calculate transfer time and simulate delay
        double transferTimeSeconds = (double) chunkSizeBits / availableBandwidth;
        try {
            Thread.sleep((long) (transferTimeSeconds * 1000));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }

        // Update chunk status
        chunk.setStatus(TransferStatus.COMPLETED);
        chunk.setStoredNode(nodeId);
        networkUtilization.addAndGet((long) (availableBandwidth * 0.8));
        totalDataTransferred.addAndGet(chunk.getSize());

        // Check if transfer is complete
        if (transfer.isComplete()) {
            transfer.markCompleted();
            usedStorage.addAndGet(transfer.getTotalSize());
            storedFiles.put(fileId, transfer);
            activeTransfers.remove(fileId);
            totalRequestsProcessed.incrementAndGet();

            logger.info("File transfer completed for {} on node {}", transfer.getFileName(), nodeId);
        }

        return true;
    }

    /**
     * Gets the current resource utilization statistics.
     *
     * @return map containing utilization statistics
     */
    public Map<String, Object> getUtilizationStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("node_id", nodeId);
        stats.put("used_storage", usedStorage.get());
        stats.put("total_storage", capacity.getStorage());
        stats.put("storage_utilization", (double) usedStorage.get() / capacity.getStorage() * 100);
        stats.put("network_utilization", networkUtilization.get());
        stats.put("total_bandwidth", capacity.getBandwidth());
        stats.put("active_transfers", activeTransfers.size());
        stats.put("stored_files", storedFiles.size());
        stats.put("total_requests", totalRequestsProcessed.get());
        stats.put("total_data_transferred", totalDataTransferred.get());
        stats.put("failed_transfers", failedTransfers.get());
        return stats;
    }

    /**
     * Shuts down the storage node gracefully.
     */
    public void shutdown() {
        if (!running) {
            return;
        }

        running = false;
        logger.info("Shutting down storage node {}", nodeId);

        // Stop heartbeat components
        if (heartbeatSender != null) {
            heartbeatSender.stopSender();
        }
        if (heartbeatServer != null) {
            heartbeatServer.stopServer();
        }

        // Shutdown thread pools
        if (scheduledExecutor != null && !scheduledExecutor.isShutdown()) {
            scheduledExecutor.shutdown();
        }
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }

        logger.info("Storage node {} shutdown complete", nodeId);
    }

    // Getters for external access
    public String getNodeId() {
        return nodeId;
    }

    public NodeCapacity getCapacity() {
        return capacity;
    }

    public boolean isRunning() {
        return running;
    }

    public Map<String, FileTransfer> getActiveTransfers() {
        return new HashMap<>(activeTransfers);
    }

    public Map<String, FileTransfer> getStoredFiles() {
        return new HashMap<>(storedFiles);
    }

    public Map<String, NodeConnection> getConnections() {
        return new HashMap<>(connections);
    }

    public long getUsedStorage() {
        return usedStorage.get();
    }

    public long getNetworkUtilization() {
        return networkUtilization.get();
    }

    public long getTotalRequestsProcessed() {
        return totalRequestsProcessed.get();
    }

    public long getTotalDataTransferred() {
        return totalDataTransferred.get();
    }

    public long getFailedTransfers() {
        return failedTransfers.get();
    }
}